
current_dir=$(dirname "$(readlink -f "$0")")
cd $current_dir
export DOCKER_WORKPLACE_NAME=workplace_paper

export BASE_IMAGES=tjbtech1/paperagent:latest

# 使用本地大模型配置
export COMPLETION_MODEL="openai/gpt-4o-2024-08-06"
export CHEEP_MODEL="openai/gpt-4o-2024-08-06"
export API_BASE_URL="http://localhost:8321/v1"
export EMBEDDING_MODEL="/media/sc/AI/self-llm/embed_model/sentence-transformers/all-MiniLM-L6-v2"


category=vq
instance_id=one_layer_vq
# 使用单GPU
export GPUS='"device=0"'

# 更改端口号为12375，避免端口冲突
python run_infer_idea.py --instance_path ../benchmark/final/${category}/${instance_id}.json --container_name paper_eval --model $COMPLETION_MODEL --workplace_name workplace --cache_path cache --port 12375 --max_iter_times 3 --category ${category}

